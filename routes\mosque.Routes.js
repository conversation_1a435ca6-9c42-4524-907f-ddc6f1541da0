const express = require('express');
const mosqueController = require('../controllers/mosque.Controller');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Location:
 *       type: object
 *       properties:
 *         latitude:
 *           type: number
 *           description: Latitude coordinate
 *         longitude:
 *           type: number
 *           description: Longitude coordinate
 *         city:
 *           type: string
 *           description: City name
 *         country:
 *           type: string
 *           description: Country name
 *         region:
 *           type: string
 *           description: Region/state name
 *
 *     Mosque:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Name of the mosque
 *         address:
 *           type: string
 *           description: Address of the mosque
 *         coordinates:
 *           type: object
 *           properties:
 *             lat:
 *               type: number
 *             lng:
 *               type: number
 *         denomination:
 *           type: string
 *           nullable: true
 *           description: Religious denomination (OSM data)
 *         website:
 *           type: string
 *           nullable: true
 *           description: Website URL (OSM data)
 *         phone:
 *           type: string
 *           nullable: true
 *           description: Phone number (OSM data)
 *
 *     MosqueResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         location:
 *           $ref: '#/components/schemas/Location'
 *         mosques:
 *           type: object
 *           properties:
 *             count:
 *               type: number
 *             data:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Mosque'
 *             dataSource:
 *               type: string
 *               enum: [openstreetmap]
 *             searchRadius:
 *               type: number
 *         timestamp:
 *           type: string
 *           format: date-time
 *         requestInfo:
 *           type: object
 *           properties:
 *             clientIP:
 *               type: string
 *             searchRadius:
 *               type: number
 *             dataSource:
 *               type: string

 */

/**
 * @swagger
 * /api/mosques/nearby:
 *   get:
 *     summary: Find nearby mosques based on IP location
 *     description: Detects user location from IP address and finds nearby mosques within specified radius
 *     tags: [Mosques]
 *     parameters:
 *       - in: query
 *         name: radius
 *         schema:
 *           type: integer
 *           minimum: 100
 *           maximum: 50000
 *           default: 5000
 *         description: Search radius in meters

 *     responses:
 *       200:
 *         description: Successfully found nearby mosques
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MosqueResponse'
 *       400:
 *         description: Bad request (invalid parameters)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                 code:
 *                   type: string
 *       404:
 *         description: No mosques found in the area
 *       500:
 *         description: Internal server error
 */
router.get('/nearby', mosqueController.findNearbyMosques);

/**
 * @swagger
 * /api/mosques/health:
 *   get:
 *     summary: Service health check
 *     description: Check the health status and feature availability of the mosque service
 *     tags: [Mosques]
 *     responses:
 *       200:
 *         description: Service health information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 service:
 *                   type: string
 *                 status:
 *                   type: string
 *                   enum: [healthy, unhealthy]
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 version:
 *                   type: string
 *                 features:
 *                   type: object
 *                   properties:
 *                     ipGeolocation:
 *                       type: string
 *                     mosqueData:
 *                       type: object
 *                       properties:
 *                         openstreetmap:
 *                           type: boolean

 *                 endpoints:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       path:
 *                         type: string
 *                       method:
 *                         type: string
 *                       description:
 *                         type: string
 */
router.get('/health', mosqueController.getServiceHealth);

/**
 * @swagger
 * /api/mosques/docs:
 *   get:
 *     summary: API documentation
 *     description: Get detailed API documentation and usage information
 *     tags: [Mosques]
 *     responses:
 *       200:
 *         description: API documentation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 api:
 *                   type: string
 *                 version:
 *                   type: string
 *                 description:
 *                   type: string
 *                 baseUrl:
 *                   type: string
 *                 endpoints:
 *                   type: object
 *                 responseFormat:
 *                   type: object
 *                 errorCodes:
 *                   type: object
 */
router.get('/docs', mosqueController.getApiDocs);



module.exports = router;

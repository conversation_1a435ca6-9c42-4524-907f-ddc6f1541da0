name: Auto Merge atif-branch into Main

on:
  push:
    branches:
      - atif-branch

jobs:
  merge:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Needed to fetch all branches

      - name: Set up Git
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"

      - name: Fetch all branches
        run: git fetch --all

      - name: Checkout main branch
        run: git checkout main

      - name: Merge atif-branch into main
        run: |
          git merge origin/atif-branch --allow-unrelated-histories --no-ff -m "Auto-merged atif-branch into main"
          git push origin main
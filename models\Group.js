const mongoose = require('mongoose');

const GroupSchema = new mongoose.Schema({
  conversation: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Conversation',
    required: true
  },
  name: {
    type: String,
    required: true
  },
  description: String,
  image: String,
  admin: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Step2',
    required: true
  },
  admins: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Step2'
  }],
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Group', GroupSchema);
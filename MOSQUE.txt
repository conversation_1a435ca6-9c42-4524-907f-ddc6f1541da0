# Mosque Finder API

A backend-only Node.js + Express API that detects user location from IP address and finds nearby mosques within a specified radius.

## Features

- **IP-based Location Detection**: Automatically detects user location using IP geolocation (ip-api.com)
- **Multiple Data Sources**: Supports both Google Places API and OpenStreetMap Overpass API
- **Static Map Generation**: Creates visual maps showing user location and nearby mosques
- **Production Ready**: Includes error handling, rate limiting, and environment configuration
- **Comprehensive API**: Health checks, documentation endpoints, and detailed responses

## API Endpoints

### Main Endpoint
- `GET /api/mosques/nearby` - Find nearby mosques based on IP location

### Utility Endpoints
- `GET /api/mosques/health` - Service health check
- `GET /api/mosques/docs` - API documentation
- `GET /health` - General server health check

## Installation & Setup

### 1. Install Dependencies

The required dependency `axios` should already be installed. If not, run:

```bash
npm install axios
```

### 2. Configure Environment Variables

Update your `.env` file with the required API keys:

```env
# Google API Keys for Mosque Finder (Optional but recommended)
GOOGLE_PLACES_API_KEY=your_google_places_api_key_here
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
```

### 3. Get Google API Keys (Optional but Recommended)

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - **Places API** (for finding mosques)
   - **Maps Static API** (for generating map images)
4. Create credentials (API Key)
5. Add the API key to your `.env` file

**Note**: The API works without Google API keys by falling back to OpenStreetMap, but Google Places provides more comprehensive data.

### 4. Start the Server

```bash
npm start
# or
npm run dev
```

The server will start on `http://localhost:5000` (or your configured PORT).

## API Usage

### Find Nearby Mosques

**Endpoint**: `GET /api/mosques/nearby`

**Parameters**:
- `radius` (optional): Search radius in meters (100-50000, default: 5000)
- `useGoogle` (optional): Use Google Places API (true/false, default: true)
- `includeMap` (optional): Include static map URL (true/false, default: true)

**Example Requests**:

```bash
# Basic request
curl "http://localhost:5000/api/mosques/nearby"

# With custom radius
curl "http://localhost:5000/api/mosques/nearby?radius=3000"

# Force OpenStreetMap usage
curl "http://localhost:5000/api/mosques/nearby?useGoogle=false"

# Without map image
curl "http://localhost:5000/api/mosques/nearby?includeMap=false"
```

**Example Response**:

```json
{
  "success": true,
  "location": {
    "latitude": 40.7128,
    "longitude": -74.0060,
    "city": "New York",
    "country": "United States",
    "region": "New York"
  },
  "mosques": {
    "count": 5,
    "data": [
      {
        "name": "Islamic Center of New York",
        "address": "1711 3rd Ave, New York, NY 10128",
        "coordinates": {
          "lat": 40.7831,
          "lng": -73.9712
        },
        "rating": 4.2,
        "placeId": "ChIJXxxxxxxxxxxxxxxx",
        "openNow": true
      }
    ],
    "dataSource": "google_places",
    "searchRadius": 5000
  },
  "mapImageUrl": "https://maps.googleapis.com/maps/api/staticmap?size=600x400&zoom=13&maptype=roadmap&key=YOUR_API_KEY&markers=color:red|label:U|40.7128,-74.0060&markers=color:blue|label:A|40.7831,-73.9712",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "requestInfo": {
    "clientIP": "***********",
    "searchRadius": 5000,
    "dataSource": "google_places",
    "includeMap": true,
    "useGoogleAPI": true
  }
}
```

### Health Check

**Endpoint**: `GET /api/mosques/health`

```bash
curl "http://localhost:5000/api/mosques/health"
```

**Response**:
```json
{
  "success": true,
  "service": "Mosque Finder API",
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "version": "1.0.0",
  "features": {
    "ipGeolocation": "ip-api.com",
    "mosqueData": {
      "google": true,
      "openstreetmap": true
    },
    "staticMaps": true
  }
}
```

## Data Sources

### 1. IP Geolocation
- **Primary**: [ip-api.com](http://ip-api.com) (Free, no API key required)
- **Fallback**: Returns New York coordinates for localhost/development

### 2. Mosque Data
- **Primary**: Google Places API (requires API key)
- **Fallback**: OpenStreetMap Overpass API (free, no API key required)

### 3. Static Maps
- **Primary**: Google Static Maps API (requires API key)
- **Fallback**: Returns null if no API key available

## Error Handling

The API includes comprehensive error handling with specific error codes:

- `INVALID_RADIUS`: Radius parameter is invalid (must be 100-50000 meters)
- `LOCATION_DETECTION_FAILED`: Unable to detect location from IP address
- `SERVICE_UNAVAILABLE`: External service temporarily unavailable
- `NO_MOSQUES_FOUND`: No mosques found in the specified area
- `INTERNAL_SERVER_ERROR`: Generic server error

## Development Notes

### Testing Locally

When testing locally, the API automatically detects localhost IPs and returns New York coordinates for development purposes.

### Rate Limiting

Consider implementing rate limiting for production use:

```javascript
const rateLimit = require('express-rate-limit');

const mosqueRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});

app.use('/api/mosques', mosqueRateLimit);
```

### Production Deployment

1. Set proper environment variables
2. Configure CORS for your frontend domain
3. Use HTTPS in production
4. Monitor API usage and costs (Google APIs)
5. Implement proper logging and monitoring

## API Costs

- **ip-api.com**: Free (1000 requests/month, then paid)
- **OpenStreetMap**: Free (respect usage policies)
- **Google Places API**: Paid (see Google Cloud pricing)
- **Google Static Maps API**: Paid (see Google Cloud pricing)

## Swagger Documentation

The API is integrated with the existing Swagger documentation. Visit:
- `http://localhost:5000/api-docs` - Swagger UI
- `http://localhost:5000/swagger.json` - Swagger JSON spec

## Support

For issues or questions:
1. Check the health endpoint: `/api/mosques/health`
2. Review the API documentation: `/api/mosques/docs`
3. Check server logs for detailed error messages

## License

This project is part of the aldallah_be backend system.

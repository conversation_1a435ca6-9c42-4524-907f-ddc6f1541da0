const Conversation = require('../models/Conversation');
const Message = require('../models/Message');
const Step2 = require('../models/Step2.Model');

// Store active socket connections
const activeUsers = new Map(); // userId -> socketId
const userSockets = new Map(); // socketId -> userId

const initializeChatSocket = (io) => {
  io.on('connection', (socket) => {
    console.log(`User connected: ${socket.id}`);


    socket.on('join', async (data) => {
      try {
        const { userId } = data;

        if (!userId) {
          socket.emit('error', { message: 'User ID is required' });
          return;
        }

        // Verify user exists
        const user = await Step2.findById(userId);
        if (!user) {
          socket.emit('error', { message: 'User not found' });
          return;
        }

        // Store user connection
        activeUsers.set(userId, socket.id);
        userSockets.set(socket.id, userId);

        // Join user to their personal room
        socket.join(`user_${userId}`);

        // Get user's conversations and join those rooms
        const conversations = await Conversation.find({
          participants: userId
        });

        conversations.forEach(conversation => {
          socket.join(`conversation_${conversation._id}`);
        });


        socket.emit('connected', {
          message: 'Successfully connected to chat',
          userId: userId
        });

        // Notify other users that this user is online
        socket.broadcast.emit('user_online', { userId });

        console.log(`User ${userId} joined chat`);
      } catch (error) {
        console.error('Error in join event:', error);
        socket.emit('error', { message: 'Failed to join chat' });
      }
    });

    // Handle sending messages
    socket.on('send_message', async (data) => {
      try {
        const { conversationId, senderId, content, messageType = 'text' } = data;

        if (!conversationId || !senderId || !content) {
          socket.emit('error', { message: 'Missing required fields' });
          return;
        }

        // Verify conversation exists and sender is a participant
        const conversation = await Conversation.findById(conversationId);
        if (!conversation) {
          socket.emit('error', { message: 'Conversation not found' });
          return;
        }

        if (!conversation.participants.includes(senderId)) {
          socket.emit('error', { message: 'Not a participant in this conversation' });
          return;
        }

        // Create new message
        const message = new Message({
          conversation: conversationId,
          sender: senderId,
          content,
          messageType,
          deliveredTo: [senderId] // Mark as delivered to sender immediately
        });

        await message.save();
        await message.populate('sender', 'username image');

        // Update conversation's last message and unread counts
        conversation.lastMessage = message._id;

        // Increment unread count for other participants
        conversation.unreadCounts.forEach(unreadCount => {
          if (unreadCount.user.toString() !== senderId) {
            unreadCount.count += 1;
          }
        });

        await conversation.save();

        // Emit message to all participants in the conversation
        io.to(`conversation_${conversationId}`).emit('new_message', {
          message,
          conversationId
        });

        // Mark message as delivered to online participants
        const onlineParticipants = conversation.participants.filter(participantId => {
          return participantId.toString() !== senderId && activeUsers.has(participantId.toString());
        });

        if (onlineParticipants.length > 0) {
          await Message.updateOne(
            { _id: message._id },
            { $addToSet: { deliveredTo: { $each: onlineParticipants } } }
          );

          // Emit delivery status to sender
          socket.emit('message_delivered', {
            messageId: message._id,
            deliveredTo: onlineParticipants
          });
        }

        console.log(`Message sent in conversation ${conversationId}`);
      } catch (error) {
        console.error('Error sending message:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Handle joining a conversation
    socket.on('join_conversation', async (data) => {
      try {
        const { conversationId, userId } = data;

        if (!conversationId || !userId) {
          socket.emit('error', { message: 'Conversation ID and User ID are required' });
          return;
        }

        // Verify user is a participant
        const conversation = await Conversation.findById(conversationId);
        if (!conversation || !conversation.participants.includes(userId)) {
          socket.emit('error', { message: 'Not authorized to join this conversation' });
          return;
        }

        socket.join(`conversation_${conversationId}`);
        socket.emit('joined_conversation', { conversationId });

        console.log(`User ${userId} joined conversation ${conversationId}`);
      } catch (error) {
        console.error('Error joining conversation:', error);
        socket.emit('error', { message: 'Failed to join conversation' });
      }
    });

    // Handle leaving a conversation
    socket.on('leave_conversation', (data) => {
      const { conversationId } = data;
      if (conversationId) {
        socket.leave(`conversation_${conversationId}`);
        socket.emit('left_conversation', { conversationId });
        console.log(`User left conversation ${conversationId}`);
      }
    });

    // Handle marking messages as read
    socket.on('mark_as_read', async (data) => {
      try {
        const { conversationId, userId } = data;

        if (!conversationId || !userId) {
          socket.emit('error', { message: 'Conversation ID and User ID are required' });
          return;
        }

        // Update messages as read
        const result = await Message.updateMany(
          {
            conversation: conversationId,
            sender: { $ne: userId },
            readBy: { $ne: userId }
          },
          {
            $addToSet: { readBy: userId }
          }
        );

        // Reset unread count
        await Conversation.updateOne(
          { _id: conversationId, 'unreadCounts.user': userId },
          { $set: { 'unreadCounts.$.count': 0 } }
        );

        // Notify other participants that messages were read
        socket.to(`conversation_${conversationId}`).emit('messages_read', {
          conversationId,
          readBy: userId,
          readCount: result.modifiedCount
        });

        socket.emit('marked_as_read', {
          conversationId,
          readCount: result.modifiedCount
        });

        console.log(`User ${userId} marked ${result.modifiedCount} messages as read in conversation ${conversationId}`);
      } catch (error) {
        console.error('Error marking messages as read:', error);
        socket.emit('error', { message: 'Failed to mark messages as read' });
      }
    });

    // Handle typing indicators
    socket.on('typing_start', (data) => {
      const { conversationId, userId } = data;
      if (conversationId && userId) {
        socket.to(`conversation_${conversationId}`).emit('user_typing', {
          conversationId,
          userId,
          isTyping: true
        });
      }
    });

    socket.on('typing_stop', (data) => {
      const { conversationId, userId } = data;
      if (conversationId && userId) {
        socket.to(`conversation_${conversationId}`).emit('user_typing', {
          conversationId,
          userId,
          isTyping: false
        });
      }
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      const userId = userSockets.get(socket.id);

      if (userId) {
        // Remove user from active users
        activeUsers.delete(userId);
        userSockets.delete(socket.id);

        // Notify other users that this user is offline
        socket.broadcast.emit('user_offline', { userId });

        console.log(`User ${userId} disconnected`);
      } else {
        console.log(`Unknown user disconnected: ${socket.id}`);
      }
    });

    // Handle errors
    socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  });

  // Helper function to get online users
  const getOnlineUsers = () => {
    return Array.from(activeUsers.keys());
  };

  // Helper function to check if user is online
  const isUserOnline = (userId) => {
    return activeUsers.has(userId);
  };

  return {
    getOnlineUsers,
    isUserOnline,
    activeUsers,
    userSockets
  };
};

module.exports = initializeChatSocket;

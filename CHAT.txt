# Chat System Documentation

## Overview

This is a simple user-to-user chat system built with Node.js, Express, Socket.IO, and MongoDB. It provides real-time messaging capabilities similar to WhatsApp with message status tracking (sent, delivered, read).

## Features

- ✅ Real-time messaging using Socket.IO
- ✅ One-to-one conversations
- ✅ Message status tracking (sent, delivered, read)
- ✅ Typing indicators
- ✅ Online/offline status
- ✅ Message history with pagination
- ✅ Unread message counts
- ✅ REST API endpoints for chat operations
- ✅ No authentication required (as requested)

## Database Schema

### Users (Step2 Model)
```javascript
{
  username: String,
  email: String,
  phoneNumber: String,
  image: String,
  isOnline: Boolean,
  lastSeen: Date,
  socketId: String
}
```

### Conversations
```javascript
{
  participants: [ObjectId], // Array of user IDs
  isGroup: Boolean,
  lastMessage: ObjectId,
  unreadCounts: [{
    user: ObjectId,
    count: Number
  }]
}
```

### Messages
```javascript
{
  conversation: ObjectId,
  sender: ObjectId,
  content: String,
  messageType: String, // 'text', 'image', 'audio', 'location', 'system'
  readBy: [ObjectId],
  deliveredTo: [ObjectId],
  isDeleted: Boolean
}
```

## API Endpoints

### REST API

#### 1. Create or Get Conversation
```
POST /api/chat/conversations/:userId
Body: { "participantId": "user_id" }
```

#### 2. Get User Conversations
```
GET /api/chat/conversations/:userId
```

#### 3. Send Message
```
POST /api/chat/conversations/:conversationId/messages
Body: {
  "senderId": "user_id",
  "content": "message content",
  "messageType": "text"
}
```

#### 4. Get Messages
```
GET /api/chat/conversations/:conversationId/messages?page=1&limit=50
```

#### 5. Mark Messages as Read
```
PUT /api/chat/conversations/:conversationId/read
Body: { "userId": "user_id" }
```

#### 6. Mark Messages as Delivered
```
PUT /api/chat/conversations/:conversationId/delivered
Body: { "userId": "user_id" }
```

### Socket.IO Events

#### Client to Server Events

1. **join** - Connect user to chat
   ```javascript
   socket.emit('join', { userId: 'user_id' });
   ```

2. **send_message** - Send a message
   ```javascript
   socket.emit('send_message', {
     conversationId: 'conversation_id',
     senderId: 'user_id',
     content: 'message content',
     messageType: 'text'
   });
   ```

3. **join_conversation** - Join a conversation room
   ```javascript
   socket.emit('join_conversation', {
     conversationId: 'conversation_id',
     userId: 'user_id'
   });
   ```

4. **mark_as_read** - Mark messages as read
   ```javascript
   socket.emit('mark_as_read', {
     conversationId: 'conversation_id',
     userId: 'user_id'
   });
   ```

5. **typing_start/typing_stop** - Typing indicators
   ```javascript
   socket.emit('typing_start', {
     conversationId: 'conversation_id',
     userId: 'user_id'
   });
   ```

#### Server to Client Events

1. **connected** - User successfully connected
2. **new_message** - New message received
3. **message_delivered** - Message delivery confirmation
4. **messages_read** - Messages read confirmation
5. **user_typing** - Typing indicator
6. **user_online/user_offline** - User status changes
7. **error** - Error messages

## Usage Example

### 1. Start the Server
```bash
npm start
```

### 2. Connect to Chat (Client-side)
```javascript
const socket = io('http://localhost:5000');

// Connect user
socket.emit('join', { userId: 'your_user_id' });

// Listen for connection confirmation
socket.on('connected', (data) => {
  console.log('Connected to chat:', data);
});
```

### 3. Create a Conversation
```javascript
// REST API call
const response = await fetch('http://localhost:5000/api/chat/conversations/user1', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ participantId: 'user2' })
});
```

### 4. Send Messages
```javascript
// Via Socket.IO (real-time)
socket.emit('send_message', {
  conversationId: 'conversation_id',
  senderId: 'user1',
  content: 'Hello!',
  messageType: 'text'
});

// Via REST API
const response = await fetch('http://localhost:5000/api/chat/conversations/conversation_id/messages', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    senderId: 'user1',
    content: 'Hello!',
    messageType: 'text'
  })
});
```

### 5. Listen for Messages
```javascript
socket.on('new_message', (data) => {
  console.log('New message:', data.message);
  // Display message in UI
});
```

## Testing the System

1. **Open the example HTML file** in `examples/chat-client-example.html`
2. **Create two users** in your database (Step2 collection)
3. **Open two browser tabs** with the example page
4. **Connect each tab** with different user IDs
5. **Start a conversation** and test messaging

## Message Status Flow

1. **Sent**: Message is created and stored in database
2. **Delivered**: Message reaches online recipients (automatic via Socket.IO)
3. **Read**: Recipient explicitly marks messages as read

## Security Considerations

⚠️ **Important**: This implementation has no authentication as requested. For production use, consider adding:

- User authentication and authorization
- Input validation and sanitization
- Rate limiting
- CORS configuration
- Message encryption
- File upload security (for media messages)

## Dependencies

- express: Web framework
- socket.io: Real-time communication
- mongoose: MongoDB ODM
- cors: Cross-origin resource sharing

## File Structure

```
├── controllers/
│   └── chat.Controller.js     # Chat REST API controllers
├── models/
│   ├── Conversation.js        # Conversation model
│   ├── Message.js            # Message model
│   └── Step2.Model.js        # User model (enhanced)
├── routes/
│   └── chat.Routes.js        # Chat API routes
├── socket/
│   └── chatSocket.js         # Socket.IO chat handlers
├── examples/
│   └── chat-client-example.html  # Example client implementation
└── server.js                 # Main server file (updated)
```

## Next Steps

To extend this chat system, you could add:

- Group chat functionality
- Media message support (images, audio, video)
- Message reactions and replies
- Push notifications
- Message search
- User blocking/reporting
- Message encryption
- Voice/video calling integration
